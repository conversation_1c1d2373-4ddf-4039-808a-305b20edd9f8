import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { businessIdeal, description, messages } = await request.json();

    if (!businessIdeal || !description) {
      return NextResponse.json({ message: 'Business ideal and description are required.' }, { status: 400 });
    }

    // For now, use a comprehensive behavioral science framework instead of PDF parsing
    // This avoids PDF parsing issues while providing <PERSON>'s key principles
    const behavioralScienceContent = `
    <PERSON>'s Behavioral Science in Marketing - Key Principles:

    1. PSYCHOLOGICAL TRIGGERS:
    - Loss Aversion: People fear losing something more than they value gaining something equivalent
    - Social Proof: People follow the actions of others, especially similar others
    - Scarcity: Limited availability increases perceived value and urgency
    - Authority: People defer to experts and credible sources
    - Reciprocity: People feel obligated to return favors
    - Commitment & Consistency: People want to align with previous commitments

    2. COGNITIVE BIASES TO LEVERAGE:
    - Anchoring: First piece of information influences all subsequent judgments
    - Availability Heuristic: People judge likelihood by how easily examples come to mind
    - Confirmation Bias: People seek information that confirms existing beliefs
    - Endowment Effect: People value things more highly when they own them
    - Framing Effect: How information is presented affects decision-making
    - Default Bias: People tend to stick with pre-selected options

    3. EMOTIONAL TRIGGERS:
    - Fear of Missing Out (FOMO)
    - Desire for status and belonging
    - Need for control and autonomy
    - Aspiration and self-improvement
    - Security and safety concerns
    - Instant gratification vs. delayed rewards

    4. PERSUASION TECHNIQUES:
    - Storytelling that creates emotional connection
    - Social proof through testimonials and reviews
    - Authority positioning through expertise demonstration
    - Scarcity through limited-time offers
    - Reciprocity through free value provision
    - Commitment through small initial commitments

    5. CONVERSION OPTIMIZATION:
    - Reduce cognitive load in decision-making
    - Use progressive disclosure of information
    - Implement choice architecture and nudging
    - Optimize default options
    - Create clear value propositions
    - Minimize friction in user experience

    6. ETHICAL CONSIDERATIONS:
    - Focus on genuine customer value
    - Avoid manipulation and dark patterns
    - Build long-term trust and relationships
    - Ensure transparency in communications
    - Respect customer autonomy and choice
    - Use behavioral insights to help, not exploit
    `;

    let pdfContent = behavioralScienceContent;

    const prompt = `
      You are a world-class behavioral science expert and marketing strategist, deeply versed in Nancy Harhut's principles of using behavioral science in marketing. Your mission is to help this business apply Nancy Harhut's proven behavioral science techniques to achieve better marketing results and customer engagement.

      **Core Reference: Nancy Harhut's Behavioral Science in Marketing**
      ${pdfContent}

      ---

      **Business to Analyze:**
      - **Business/Product:** "${businessIdeal}"
      - **Description:** "${description}"

      ---

      **Your Task: Deep Behavioral Science Marketing Analysis**

      Provide a comprehensive, actionable analysis using Nancy Harhut's behavioral science framework. Structure your response in well-formatted markdown with the following sections:

      ## 1. 🧠 Behavioral Science Foundation Analysis
      
      **Core Behavioral Triggers Assessment:**
      - Identify the primary psychological triggers relevant to this business
      - Analyze the target audience's decision-making patterns
      - Map key behavioral biases that can be leveraged
      - Assess emotional vs. rational decision drivers

      **Behavioral Economics Application:**
      - Loss aversion opportunities
      - Social proof mechanisms
      - Scarcity and urgency applications
      - Anchoring and framing strategies

      ## 2. 🎯 Customer Psychology Deep Dive

      **Cognitive Biases to Leverage:**
      - Specific biases most relevant to this business model
      - How to ethically apply these biases in marketing
      - Timing and context considerations
      - Potential risks and mitigation strategies

      **Emotional Triggers Mapping:**
      - Primary emotional drivers for the target audience
      - Fear, desire, and aspiration triggers
      - Social status and belonging motivations
      - Trust and credibility building elements

      ## 3. 🚀 Behavioral Marketing Strategy

      **Persuasion Techniques Implementation:**
      - Specific Nancy Harhut techniques applicable to this business
      - Message framing and positioning strategies
      - Call-to-action optimization using behavioral principles
      - Customer journey behavioral touchpoints

      **Social Psychology Applications:**
      - Social proof strategies (testimonials, reviews, user counts)
      - Authority and expertise positioning
      - Reciprocity and commitment techniques
      - Community and belonging creation

      ## 4. 📈 Conversion Optimization Framework

      **Behavioral Conversion Tactics:**
      - Specific techniques to reduce friction and increase conversions
      - Psychological pricing strategies
      - Choice architecture optimization
      - Default options and nudging techniques

      **Testing and Measurement:**
      - Key behavioral metrics to track
      - A/B testing frameworks for behavioral elements
      - Customer feedback loops for behavioral insights
      - Long-term behavioral pattern analysis

      ## 5. 🎨 Creative and Messaging Strategy

      **Behavioral Copywriting:**
      - Headline and subject line optimization using behavioral triggers
      - Storytelling techniques that leverage psychological principles
      - Visual and design elements that support behavioral goals
      - Personalization strategies based on behavioral segments

      **Channel-Specific Applications:**
      - Email marketing behavioral techniques
      - Social media psychological triggers
      - Website and landing page behavioral optimization
      - Advertising creative behavioral elements

      ## 6. ⚠️ Ethical Considerations and Best Practices

      **Ethical Behavioral Marketing:**
      - Guidelines for ethical application of behavioral science
      - Transparency and trust-building measures
      - Long-term relationship vs. short-term manipulation
      - Customer value and genuine benefit focus

      **Implementation Roadmap:**
      - Phase 1: Quick wins and immediate implementations
      - Phase 2: Medium-term behavioral strategy development
      - Phase 3: Advanced behavioral science integration
      - Success metrics and evaluation criteria

      ## 7. 🔄 Continuous Improvement Framework

      **Behavioral Learning System:**
      - Customer behavior data collection strategies
      - Behavioral pattern analysis and insights
      - Iterative improvement based on behavioral feedback
      - Scaling successful behavioral interventions

      **Advanced Applications:**
      - Predictive behavioral modeling opportunities
      - Personalization at scale using behavioral data
      - Cross-channel behavioral consistency
      - Innovation opportunities using behavioral insights

      ---

      **Key Success Factors:**
      - Focus on ethical, customer-centric applications
      - Provide specific, actionable recommendations
      - Include real-world examples and case studies where relevant
      - Balance psychological effectiveness with genuine value creation
      - Consider both immediate tactics and long-term behavioral strategy

      **Remember:** Every recommendation should be grounded in Nancy Harhut's behavioral science principles and adapted specifically to this business context. Be specific, practical, and transformative in your suggestions while maintaining ethical standards.
    `;

    // Call the OpenRouter API
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App - Nancy Harhut Behavioral Science Analysis',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-flash',
        messages: [
          ...(messages || []),
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: true,
      }),
    });

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.text();
      console.error('OpenRouter API error:', errorData);
      return NextResponse.json({ message: 'Failed to get response from AI service.' }, { status: 500 });
    }

    // Return the streaming response
    return new Response(openRouterResponse.body, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
      },
    });
  } catch (error) {
    console.error('Error in Nancy Harhut analysis:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
