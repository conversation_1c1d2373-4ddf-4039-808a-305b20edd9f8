"use client";

import React, { useState, useEffect } from 'react';
import AIChatbox from '@/components/AIChatbox';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const LeiJun2Page = () => {
  const [businessIdeal, setBusinessIdeal] = useState('');
  const [description, setDescription] = useState('');
  const [analysisResult, setAnalysisResult] = useState('');
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [shouldSendForgetPrompt, setShouldSendForgetPrompt] = useState(false);
  const [showCopyMessage, setShowCopyMessage] = useState(false);

  // Load data from localStorage on component mount
  useEffect(() => {
    const savedBusinessIdeal = localStorage.getItem('leijun2BusinessIdeal');
    const savedDescription = localStorage.getItem('leijun2Description');
    const savedAnalysisResult = localStorage.getItem('leijun2AnalysisResult');
    const savedChatMessages = localStorage.getItem('leijun2ChatMessages');

    if (savedBusinessIdeal) setBusinessIdeal(savedBusinessIdeal);
    if (savedDescription) setDescription(savedDescription);
    if (savedAnalysisResult) setAnalysisResult(savedAnalysisResult);
    if (savedChatMessages) {
      try {
        setChatMessages(JSON.parse(savedChatMessages));
      } catch (e) {
        console.error('Failed to parse saved chat messages:', e);
      }
    }
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    localStorage.setItem('leijun2BusinessIdeal', businessIdeal);
  }, [businessIdeal]);

  useEffect(() => {
    localStorage.setItem('leijun2Description', description);
  }, [description]);

  useEffect(() => {
    localStorage.setItem('leijun2AnalysisResult', analysisResult);
  }, [analysisResult]);

  useEffect(() => {
    localStorage.setItem('leijun2ChatMessages', JSON.stringify(chatMessages));
  }, [chatMessages]);

  const handleAnalyze = async () => {
    setLoading(true);
    setError('');
    setAnalysisResult('');
    setChatMessages([]);

    let messagesToSend = [];
    if (shouldSendForgetPrompt) {
      messagesToSend.push({ role: "system", content: "User has initiated a full reset. Disregard all previous conversation history and start fresh." });
      setShouldSendForgetPrompt(false);
    }
    
    try {
      const response = await fetch('/api/analyze-leijun-2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ businessIdeal, description, messages: messagesToSend }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Something went wrong with the analysis.');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get reader for response body.');
      }

      const decoder = new TextDecoder();
      let receivedText = '';
      setAnalysisResult('');

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }
        receivedText += decoder.decode(value, { stream: true });
        setAnalysisResult(receivedText);
      }
      setChatMessages([{ role: "assistant", content: receivedText }]);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    if (window.confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      setBusinessIdeal('');
      setDescription('');
      setAnalysisResult('');
      setChatMessages([]);
      localStorage.removeItem('leijun2BusinessIdeal');
      localStorage.removeItem('leijun2Description');
      localStorage.removeItem('leijun2AnalysisResult');
      localStorage.removeItem('leijun2ChatMessages');
      setShouldSendForgetPrompt(true);
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(analysisResult).then(() => {
      setShowCopyMessage(true);
      setTimeout(() => setShowCopyMessage(false), 2000);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  };

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Lei Jun Methodology Deep Analysis</h1>
      <p className="mb-6">
        This enhanced analysis applies Lei Jun&apos;s proven methodology from Xiaomi&apos;s success story.
        Get actionable insights based on the Seven-Character Mantra, Iron Triangle model, and explosive product principles.
      </p>

      <div className="mb-4">
        <label htmlFor="businessIdeal" className="block text-sm font-medium text-gray-700">
          Your Business/Product Idea
        </label>
        <input
          type="text"
          id="businessIdeal"
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          value={businessIdeal}
          onChange={(e) => setBusinessIdeal(e.target.value)}
          placeholder='e.g., &quot;Smart fitness tracker&quot;, &quot;AI-powered language learning app&quot;'
        />
      </div>

      <div className="mb-4">
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Detailed Description
        </label>
        <textarea
          id="description"
          rows={4}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Describe your business idea, target market, key features, competitive landscape, and your vision..."
        />
      </div>

      <div className="flex space-x-4 mb-6">
        <button
          onClick={handleAnalyze}
          disabled={loading || !businessIdeal.trim() || !description.trim()}
          className="py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Analyzing...' : 'Apply Lei Jun Methodology'}
        </button>
        <button
          onClick={handleClear}
          className="py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Clear All
        </button>
      </div>

      {error && (
        <div className="mb-4 p-4 border border-red-300 rounded-md bg-red-50">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {analysisResult && (
        <div className="space-y-6">
          <div className="border border-gray-300 rounded-md p-4 bg-gray-50">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Lei Jun Methodology Analysis</h2>
              <div className="flex space-x-2">
                <button
                  onClick={handleCopyToClipboard}
                  className="py-1 px-3 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Copy to Clipboard
                </button>
                {showCopyMessage && (
                  <span className="text-green-600 text-sm font-medium">Copied!</span>
                )}
              </div>
            </div>
            <div className="prose prose-indigo max-w-none">
              <ReactMarkdown remarkPlugins={[remarkGfm]}>{analysisResult}</ReactMarkdown>
            </div>
          </div>
          <h2 className="text-xl font-semibold mb-4">AI Chatbox for Follow-up:</h2>
          <AIChatbox
            initialMessage={"Here is the Lei Jun methodology analysis for your business:\n\n" + analysisResult}
            messages={chatMessages}
            setMessages={setChatMessages}
            shouldSendForgetPrompt={shouldSendForgetPrompt}
            resetForgetPrompt={() => setShouldSendForgetPrompt(false)}
          />
        </div>
      )}
    </div>
  );
};

export default LeiJun2Page;
