import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const { businessIdeal, description, messages } = await request.json();

    if (!businessIdeal || !description) {
      return NextResponse.json({ message: 'Business ideal and description are required.' }, { status: 400 });
    }

    // Use <PERSON>'s key behavioral science principles
    // Note: Direct PDF processing may have size/timeout issues, so using extracted principles
    const nancyHarhutContent = `
    <PERSON>'s "Using Behavioral Science in Marketing" - Core Principles:

    PART I: THE SCIENCE OF PERSUASION

    1. LOSS AVERSION
    - People feel the pain of losing something twice as intensely as the pleasure of gaining something
    - Frame messages around what customers might lose rather than what they might gain
    - Use phrases like "Don't miss out," "Limited time," "While supplies last"
    - Create urgency by highlighting potential losses from inaction

    2. SOCIAL PROOF
    - People look to others' behavior to guide their own decisions
    - Use testimonials, reviews, and user-generated content
    - Show popularity with phrases like "Join thousands of satisfied customers"
    - Leverage peer influence and community validation

    3. AUTHORITY
    - People defer to experts and credible sources
    - Use expert endorsements, certifications, and credentials
    - Position your brand as the authority in your space
    - Leverage third-party validation and media mentions

    4. RECIPROCITY
    - People feel obligated to return favors
    - Offer free value before asking for something in return
    - Use free trials, samples, and valuable content
    - Create a sense of obligation through unexpected gifts

    5. COMMITMENT AND CONSISTENCY
    - People want to align with their previous commitments and self-image
    - Get small commitments that lead to larger ones
    - Use progressive profiling and step-by-step engagement
    - Remind customers of their past positive experiences

    6. SCARCITY
    - Limited availability increases perceived value
    - Use time-limited offers and exclusive access
    - Show limited quantities or availability
    - Create FOMO (Fear of Missing Out) through exclusivity

    PART II: COGNITIVE BIASES IN MARKETING

    7. ANCHORING BIAS
    - First piece of information influences all subsequent judgments
    - Set high anchor prices to make other options seem reasonable
    - Use reference points to frame value propositions
    - Present information in strategic order

    8. AVAILABILITY HEURISTIC
    - People judge likelihood by how easily examples come to mind
    - Use vivid, memorable examples and stories
    - Make benefits concrete and easy to visualize
    - Use case studies that customers can relate to

    9. CONFIRMATION BIAS
    - People seek information that confirms existing beliefs
    - Align messaging with customers' existing worldviews
    - Reinforce what customers already believe about themselves
    - Use language that validates their current thinking

    10. ENDOWMENT EFFECT
    - People value things more highly when they own them
    - Use free trials and "try before you buy" offers
    - Create ownership through customization and personalization
    - Use language like "your account," "your dashboard"

    PART III: EMOTIONAL TRIGGERS

    11. FEAR-BASED MESSAGING
    - Fear motivates action when paired with clear solutions
    - Identify genuine fears and concerns of your audience
    - Provide clear path to resolution
    - Balance fear with hope and empowerment

    12. ASPIRATION AND STATUS
    - People want to improve their status and self-image
    - Connect products to desired identity and lifestyle
    - Use aspirational imagery and messaging
    - Create exclusive communities and experiences

    13. INSTANT GRATIFICATION
    - People prefer immediate rewards over delayed benefits
    - Emphasize immediate benefits and quick results
    - Reduce friction in the purchase process
    - Provide instant access and immediate value

    PART IV: PRACTICAL APPLICATION FRAMEWORKS

    14. CHOICE ARCHITECTURE
    - How options are presented affects decisions
    - Use default options strategically
    - Limit choices to avoid decision paralysis
    - Guide customers toward optimal decisions

    15. FRAMING EFFECTS
    - How information is presented changes perception
    - Frame benefits in terms of gains or losses strategically
    - Use positive or negative framing based on context
    - Present information in customer-friendly terms

    16. BEHAVIORAL SEGMENTATION
    - Different behavioral triggers work for different people
    - Segment audiences based on psychological profiles
    - Tailor messaging to specific behavioral patterns
    - Test different approaches with different segments

    ETHICAL CONSIDERATIONS:
    - Use behavioral science to help, not manipulate customers
    - Focus on genuine value and customer benefit
    - Build long-term trust and relationships
    - Avoid dark patterns and deceptive practices
    - Ensure transparency in communications
    - Respect customer autonomy and choice
    `;

    const prompt = `
      You are a world-class behavioral science expert and marketing strategist, deeply versed in Nancy Harhut's principles of using behavioral science in marketing. Your mission is to help this business apply Nancy Harhut's proven behavioral science techniques to achieve better marketing results and customer engagement.

      **Core Reference: Nancy Harhut's Behavioral Science Framework**
      ${nancyHarhutContent}

      ---

      **Business to Analyze:**
      - **Business/Product:** "${businessIdeal}"
      - **Description:** "${description}"

      ---

      **Your Task: Deep Behavioral Science Marketing Analysis**

      Using Nancy Harhut's behavioral science framework above, provide a comprehensive, actionable analysis. Structure your response in well-formatted markdown with the following sections:

      ## 1. 🧠 Behavioral Science Foundation Analysis
      
      **Core Behavioral Triggers Assessment:**
      - Identify the primary psychological triggers relevant to this business
      - Analyze the target audience's decision-making patterns
      - Map key behavioral biases that can be leveraged
      - Assess emotional vs. rational decision drivers

      **Behavioral Economics Application:**
      - Loss aversion opportunities
      - Social proof mechanisms
      - Scarcity and urgency applications
      - Anchoring and framing strategies

      ## 2. 🎯 Customer Psychology Deep Dive

      **Cognitive Biases to Leverage:**
      - Specific biases most relevant to this business model
      - How to ethically apply these biases in marketing
      - Timing and context considerations
      - Potential risks and mitigation strategies

      **Emotional Triggers Mapping:**
      - Primary emotional drivers for the target audience
      - Fear, desire, and aspiration triggers
      - Social status and belonging motivations
      - Trust and credibility building elements

      ## 3. 🚀 Behavioral Marketing Strategy

      **Persuasion Techniques Implementation:**
      - Specific Nancy Harhut techniques applicable to this business
      - Message framing and positioning strategies
      - Call-to-action optimization using behavioral principles
      - Customer journey behavioral touchpoints

      **Social Psychology Applications:**
      - Social proof strategies (testimonials, reviews, user counts)
      - Authority and expertise positioning
      - Reciprocity and commitment techniques
      - Community and belonging creation

      ## 4. 📈 Conversion Optimization Framework

      **Behavioral Conversion Tactics:**
      - Specific techniques to reduce friction and increase conversions
      - Psychological pricing strategies
      - Choice architecture optimization
      - Default options and nudging techniques

      **Testing and Measurement:**
      - Key behavioral metrics to track
      - A/B testing frameworks for behavioral elements
      - Customer feedback loops for behavioral insights
      - Long-term behavioral pattern analysis

      ## 5. 🎨 Creative and Messaging Strategy

      **Behavioral Copywriting:**
      - Headline and subject line optimization using behavioral triggers
      - Storytelling techniques that leverage psychological principles
      - Visual and design elements that support behavioral goals
      - Personalization strategies based on behavioral segments

      **Channel-Specific Applications:**
      - Email marketing behavioral techniques
      - Social media psychological triggers
      - Website and landing page behavioral optimization
      - Advertising creative behavioral elements

      ## 6. ⚠️ Ethical Considerations and Best Practices

      **Ethical Behavioral Marketing:**
      - Guidelines for ethical application of behavioral science
      - Transparency and trust-building measures
      - Long-term relationship vs. short-term manipulation
      - Customer value and genuine benefit focus

      **Implementation Roadmap:**
      - Phase 1: Quick wins and immediate implementations
      - Phase 2: Medium-term behavioral strategy development
      - Phase 3: Advanced behavioral science integration
      - Success metrics and evaluation criteria

      ## 7. 🔄 Continuous Improvement Framework

      **Behavioral Learning System:**
      - Customer behavior data collection strategies
      - Behavioral pattern analysis and insights
      - Iterative improvement based on behavioral feedback
      - Scaling successful behavioral interventions

      **Advanced Applications:**
      - Predictive behavioral modeling opportunities
      - Personalization at scale using behavioral data
      - Cross-channel behavioral consistency
      - Innovation opportunities using behavioral insights

      ---

      **Key Success Factors:**
      - Focus on ethical, customer-centric applications
      - Provide specific, actionable recommendations
      - Include real-world examples and case studies where relevant
      - Balance psychological effectiveness with genuine value creation
      - Consider both immediate tactics and long-term behavioral strategy

      **Remember:** Every recommendation should be grounded in Nancy Harhut's behavioral science principles and adapted specifically to this business context. Be specific, practical, and transformative in your suggestions while maintaining ethical standards.
    `;

    // Call the OpenRouter API
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
      return NextResponse.json({ message: 'OpenRouter API key is not configured.' }, { status: 500 });
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://leanalytics-playbook.vercel.app',
        'X-Title': 'Playbook App - Nancy Harhut Behavioral Science Analysis',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.5-flash',
        messages: [
          ...(messages || []),
          {
            role: 'user',
            content: prompt,
          },
        ],
        stream: true,
      }),
    });

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.text();
      console.error('OpenRouter API error:', errorData);
      return NextResponse.json({ message: 'Failed to get response from AI service.' }, { status: 500 });
    }

    // Create a readable stream to process the response
    const stream = new ReadableStream({
      async start(controller) {
        const reader = openRouterResponse.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        const decoder = new TextDecoder();

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  controller.close();
                  return;
                }

                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content;
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content));
                  }
                } catch (e) {
                  // Skip invalid JSON
                  continue;
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream processing error:', error);
        } finally {
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('Error in Nancy Harhut analysis:', error);
    return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
  }
}
