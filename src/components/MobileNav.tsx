"use client";

import React from "react";
import { FormattedMessage } from "react-intl";
import Link from "next/link";
import { Locale } from "@/lib/definitions";
import SidebarLink from "@/components/SidebarLink"; // Re-using SidebarLink for consistent styling

interface Props {
  locale: Locale;
  onClose: () => void;
}

export default function MobileNav({ locale, onClose }: Props) {
  return (
    <div className="fixed inset-0 z-50 bg-gray-800 overflow-y-auto">
      <div className="flex items-center justify-between h-16 w-[calc(100%-2rem)] mx-4">
        <div className="text-xl text-gray-200">Playbook App</div>
        <button
          type="button"
          className="text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-white"
          onClick={onClose}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <hr className="h-px border-t-0 bg-transparent bg-gradient-to-r from-transparent via-neutral-500 to-transparent opacity-25" />

      <div className="py-4">
        <SidebarLink href={`/${locale}/home`} onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
            />
          </svg>
          <div className="mx-4"><FormattedMessage id="common.navigation.home" /></div>
        </SidebarLink>

        <SidebarLink href={`/${locale}/leijun`} onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3.03.543M9 4.5a3 3 0 1 1 6 0M12 15.75h2.25c2.83 0 5.496 1.06 7.5 2.832V21.75H3.75v-5.418C5.754 16.81 8.42 15.75 11.25 15.75h.75Z"
            />
          </svg>
          <div className="mx-4"><FormattedMessage id="common.navigation.leijun" /></div>
        </SidebarLink>

        <SidebarLink href={`/${locale}/leijun-2`} onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z"
            />
          </svg>
          <div className="mx-4"><FormattedMessage id="common.navigation.leijun2" /></div>
        </SidebarLink>

        <SidebarLink href={`/${locale}/gary`} onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3.03.543M9 4.5a3 3 0 1 1 6 0M12 15.75h2.25c2.83 0 5.496 1.06 7.5 2.832V21.75H3.75v-5.418C5.754 16.81 8.42 15.75 11.25 15.75h.75Z"
            />
          </svg>
          <div className="mx-4"><FormattedMessage id="common.navigation.gary" /></div>
        </SidebarLink>

        <SidebarLink href={`/${locale}/gary-2`} onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"
            />
          </svg>
          <div className="mx-4"><FormattedMessage id="common.navigation.gary2" /></div>
        </SidebarLink>

        <SidebarLink href={`/${locale}/clayton-christensen`} onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3.03.543M9 4.5a3 3 0 1 1 6 0M12 15.75h2.25c2.83 0 5.496 1.06 7.5 2.832V21.75H3.75v-5.418C5.754 16.81 8.42 15.75 11.25 15.75h.75Z"
            />
          </svg>
          <div className="mx-4"><FormattedMessage id="common.navigation.clayton" /></div>
        </SidebarLink>

        <SidebarLink href={`/${locale}/clayton-christensen-2`} onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"
            />
          </svg>
          <div className="mx-4"><FormattedMessage id="common.navigation.clayton2" /></div>
        </SidebarLink>





        <SidebarLink href={`/${locale}/linerbridge`} onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.455-.343 2.905-.967 4.255A48.303 48.303 0 0 0 3 20.25c0 .207.167.375.375.375H12m-9.75-1.5c0-1.455.343-2.905.967-4.255A48.303 48.303 0 0 1 12 20.25h8.625c.207 0 .375-.168.375-.375V12m-9.75-1.5c0-1.455.343-2.905.967-4.255A48.303 48.303 0 0 1 12 4.5h8.625c.207 0 .375.168.375-.375V12m-9.75-1.5c0-1.455.343-2.905.967-4.255A48.303 48.303 0 0 1 12 4.5h8.625c.207 0 .375.168.375-.375V12"
            />
          </svg>
          <div className="mx-4"><FormattedMessage id="common.navigation.linerbridge" /></div>
        </SidebarLink>

        <SidebarLink href={`/${locale}/appreviews`} onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="size-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M10.5 6a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM12 17.25h.007v.008H12v-.008Z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18Z"
            />
          </svg>
          <div className="mx-4"><FormattedMessage id="common.navigation.appreviews" /></div>
        </SidebarLink>

      </div>
    </div>
  );
}
