{"name": "nextjs-i18n-dashboard-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@formatjs/intl-localematcher": "^0.5.4", "ai": "^4.3.16", "clsx": "^2.1.1", "negotiator": "^0.6.3", "next": "^14.2.15", "react": "^18.3.1", "react-dom": "^18.3.1", "react-intl": "^6.7.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@types/negotiator": "^0.6.3", "@types/node": "^20.16.10", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "eslint": "^8.57.1", "eslint-config-next": "^14.2.15", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "^5.6.2"}}